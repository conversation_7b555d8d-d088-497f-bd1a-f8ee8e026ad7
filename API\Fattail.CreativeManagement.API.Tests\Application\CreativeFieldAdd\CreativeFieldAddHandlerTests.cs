﻿using AutoBogus;
using Bogus;
using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;
using CreativeField = Fattail.CreativeManagement.API.Domain.CreativeFields.CreativeField;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativeFieldAdd;

[TestFixture]
public class CreativeFieldAddHandlerTests
{
    private CreativeFieldAddHandler _creativeFieldAddHandler = null!;
    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<ICreativeFieldRepository> _creativeFieldRepositoryMock = null!;

    [SetUp]
    public void SetUp ()
    {
        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(new Faker().Random.Long(1));

        _creativeFieldRepositoryMock = new Mock<ICreativeFieldRepository>();

        _creativeFieldAddHandler = new CreativeFieldAddHandler(
            _creativeFieldRepositoryMock.Object,
            _idManagerMock.Object
        );
    }

    [Test]
    public async Task Invalid_creative_field_is_not_persisted ()
    {
        CreativeFieldAddCommand addFieldCommand = new CreativeFieldAddCommand(
            null,
            CreativeFieldTypeEnum.SingleLineText,
            new List<SelectOption>().AsReadOnly());

        _creativeFieldRepositoryMock.Setup(repo => repo.FindAsync(It.IsAny<Specification<CreativeField>>()))
            .ReturnsAsync((CreativeField?)null);

        Result<CreativeFieldResult> result =
            await _creativeFieldAddHandler.Handle(addFieldCommand, CancellationToken.None);

        result.Should().BeFailure();
        _creativeFieldRepositoryMock.Verify(
            creativeFieldRepository =>
                creativeFieldRepository.CreateAsync<CreativeFieldResult>(It.IsAny<CreativeField>()),
            Times.Never());
    }

    [Test]
    public async Task Valid_creative_field_is_persisted ()
    {
        CreativeFieldAddCommand addFieldCommand = new CreativeFieldAddCommand(
            "Test Field",
            CreativeFieldTypeEnum.SingleLineText,
            new List<SelectOption>().AsReadOnly());

        _creativeFieldRepositoryMock.Setup(repo => repo.FindAsync(It.IsAny<Specification<CreativeField>>()))
            .ReturnsAsync((CreativeField?)null);

        _creativeFieldRepositoryMock.Setup(repo => repo.CreateAsync<CreativeFieldResult>(It.IsAny<CreativeField>()))
            .ReturnsAsync(new CreativeFieldResult(1, "Test Field", CreativeFieldTypeEnum.SingleLineText));

        Result<CreativeFieldResult> result =
            await _creativeFieldAddHandler.Handle(addFieldCommand, CancellationToken.None);

        result.Should().BeSuccess();
        _creativeFieldRepositoryMock.Verify(
            creativeFieldRepository =>
                creativeFieldRepository.CreateAsync<CreativeFieldResult>(It.IsAny<CreativeField>()),
            Times.Once());
    }

    [Test]
    public async Task Creative_field_with_duplicate_name_and_type_is_not_persisted ()
    {
        CreativeFieldAddCommand addFieldCommand = new CreativeFieldAddCommand(
            "Existing Field",
            CreativeFieldTypeEnum.SingleLineText,
            new List<SelectOption>().AsReadOnly());

        var existingField = CreativeField.Create(
            new CreativeFieldId(123),
            "Existing Field",
            CreativeFieldType.SingleLineText,
            new CreativeFieldUniqueNameRequirement(true),
            false,
            null,
            null).Value;

        _creativeFieldRepositoryMock.Setup(repo => repo.FindAsync(It.IsAny<Specification<CreativeField>>()))
            .ReturnsAsync(existingField);

        Result<CreativeFieldResult> result =
            await _creativeFieldAddHandler.Handle(addFieldCommand, CancellationToken.None);

        result.Should().BeFailure();
        _creativeFieldRepositoryMock.Verify(
            creativeFieldRepository =>
                creativeFieldRepository.CreateAsync<CreativeFieldResult>(It.IsAny<CreativeField>()),
            Times.Never());
    }

    [Test]
    public async Task Creative_field_with_select_options_is_persisted ()
    {
        var options = new List<SelectOption>
        {
            new(1, "Option 1"),
            new(2, "Option 2")
        }.AsReadOnly();

        CreativeFieldAddCommand addFieldCommand = new CreativeFieldAddCommand(
            "Select Field",
            CreativeFieldTypeEnum.SingleSelectOption,
            options);

        _creativeFieldRepositoryMock.Setup(repo => repo.FindAsync(It.IsAny<Specification<CreativeField>>()))
            .ReturnsAsync((CreativeField?)null);

        _creativeFieldRepositoryMock.Setup(repo => repo.CreateAsync<CreativeFieldResult>(It.IsAny<CreativeField>()))
            .ReturnsAsync(new CreativeFieldResult(1, "Select Field", CreativeFieldTypeEnum.SingleSelectOption));

        Result<CreativeFieldResult> result =
            await _creativeFieldAddHandler.Handle(addFieldCommand, CancellationToken.None);

        result.Should().BeSuccess();
        _creativeFieldRepositoryMock.Verify(
            creativeFieldRepository =>
                creativeFieldRepository.CreateAsync<CreativeFieldResult>(It.IsAny<CreativeField>()),
            Times.Once());
    }
}