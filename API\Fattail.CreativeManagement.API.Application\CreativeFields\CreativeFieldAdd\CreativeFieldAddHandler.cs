using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Specifications;
using FluentResults;
using MediatR;
using SelectOptionDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.Settings.SelectOption;

namespace Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;

public class CreativeFieldAddHandler (
    ICreativeFieldRepository creativeFieldRepository,
    IIdManager idManager)
    : IRequestHandler<CreativeFieldAddCommand, Result<CreativeFieldResult>>
{
    public async Task<Result<CreativeFieldResult>> Handle (CreativeFieldAddCommand request,
        CancellationToken cancellationToken)
    {
        var creativeFieldId = new CreativeFieldId(idManager.GetId());
        CreativeFieldType creativeFieldType = CreativeFieldType.FromValue((int)(request.Type ?? CreativeFieldTypeEnum.None));
        var creativeFieldNameInUseSpecification = new CreativeFieldNameInUseSpecification(request.Name, creativeFieldType);
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(await creativeFieldRepository.FindAsync(creativeFieldNameInUseSpecification) == null);

        var settings = new Dictionary<string, object>();

        if (request.Options.Any())
        {
            settings.Add(nameof(SelectCreativeFieldSettings.Options).ToLower(),
                request.Options.Select(x => SelectOptionDomain.Create(x.Id, x.Description)).ToList().AsReadOnly());
        }

        Result<CreativeField> creativeFieldResult = CreativeField.Create(creativeFieldId, request.Name, creativeFieldType, creativeFieldUniqueNameRequirement, false, null, settings);

        if (creativeFieldResult.IsFailed)
        {
            return creativeFieldResult.ToResult();
        }

        return await creativeFieldRepository.CreateAsync<CreativeFieldResult>(creativeFieldResult.Value);
    }
}