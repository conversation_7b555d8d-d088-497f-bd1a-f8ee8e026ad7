﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;

public record CreativeFieldResponse
(
    [property: OpenApiProperty(Description = "Creative field id")]
    long Id,
    [property: OpenApiProperty(Description = "Creative field name")]
    string Name,
    [property: OpenApiProperty(Description = "Creative field type")]
    CreativeFieldTypeEnum Type,
    [property: OpenApiProperty(Description = "Whether the creative field is predefined")]
    bool Predefined,
    [property: OpenApiProperty(Description = "OMS external identifier")]
    string? OmsExternalIdentifier,
    [property: OpenApiProperty(Description = "Creative field settings")]
    CreativeFieldSettingsResponse Settings,
    [property: OpenApiProperty(Description = "Creative field options")]
    IReadOnlyList<SelectOptionResponse>? Options
);

public record SelectOptionResponse (
    [property: OpenApiProperty(Description = "Option id")]
    long Id,
    [property: OpenApiProperty(Description = "Option description")]
    string Description
);

public abstract record CreativeFieldSettingsResponse;

public record DefaultCreativeFieldSettingsResponse () : CreativeFieldSettingsResponse;

public record SelectCreativeFieldSettingsResponse
(
    [property: OpenApiProperty(Description = "Select options")]
    IReadOnlyList<SelectOptionResponse> Options
) : CreativeFieldSettingsResponse;

public record MultiSelectCreativeFieldResponse
(
    long Id,
    string Name,
    CreativeFieldTypeEnum Type,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResponse Settings,
    IReadOnlyList<SelectOptionResponse>? Options
) : CreativeFieldResponse(Id, Name, Type, Predefined, OmsExternalIdentifier, Settings, Options);

public record SingleSelectCreativeFieldResponse
(
    long Id,
    string Name,
    CreativeFieldTypeEnum Type,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResponse Settings,
    IReadOnlyList<SelectOptionResponse>? Options
) : CreativeFieldResponse(Id, Name, Type, Predefined, OmsExternalIdentifier, Settings, Options);

//NOTE: This class has been just created for retro compatibility with the previous version of the API. Creatives shouldn't include SectionDivider fields.
public record SectionDividerCreativeFieldResponse
(
    long Id,
    string Name,
    CreativeFieldTypeEnum Type,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResponse Settings,
    IReadOnlyList<SelectOptionResponse>? Options,
    string? Content
) : CreativeFieldResponse(Id, Name, Type, Predefined, OmsExternalIdentifier, Settings, Options);