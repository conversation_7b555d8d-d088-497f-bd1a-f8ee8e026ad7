﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using JsonSubTypes;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using System.Text;
using CreativeField = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeField;
using CreativeFieldSettings = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeFieldSettings;
using DefaultCreativeFieldSettings = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.DefaultCreativeFieldSettings;
using SelectCreativeFieldSettings = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.SelectCreativeFieldSettings;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Serializers;

public sealed class NewtonsoftSerializer : CosmosSerializer
{
    private static readonly Encoding _defaultEncoding = new UTF8Encoding(false, true);
    private readonly JsonSerializer _jsonSerializer;

    public NewtonsoftSerializer ()
    {
        var serializerSettings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            NullValueHandling = NullValueHandling.Include,
        };

        serializerSettings.Converters.Add(new StringEnumConverter());

        serializerSettings.Converters.Add(JsonSubtypesConverterBuilder.Of<CreativeFieldValue>("Type")
            .RegisterSubtype<MultiFileUploadFieldValue>(CreativeFieldTypeEnum.MultiFileUpload)
            .RegisterSubtype<FileUploadFieldValue>(CreativeFieldTypeEnum.FileUpload)
            .RegisterSubtype<SingleLineTextFieldValue>(CreativeFieldTypeEnum.SingleLineText)
            .RegisterSubtype<MultiSelectOptionFieldValue>(CreativeFieldTypeEnum.MultiSelectOption)
            .RegisterSubtype<SingleSelectOptionFieldValue>(CreativeFieldTypeEnum.SingleSelectOption)
            .RegisterSubtype<MultiLineTextFieldValue>(CreativeFieldTypeEnum.MultiLineText)
            .Build());

        serializerSettings.Converters.Add(JsonSubtypesConverterBuilder.Of<CreativeTemplateCreativeField>("Type")
            .RegisterSubtype<CreativeTemplateMultiSelectCreativeField>(CreativeFieldTypeEnum.MultiSelectOption)
            .RegisterSubtype<CreativeTemplateSingleSelectCreativeField>(CreativeFieldTypeEnum.SingleSelectOption)
            .RegisterSubtype<CreativeTemplateSectionDividerCreativeField>(CreativeFieldTypeEnum.SectionDivider)
            .Build());

        serializerSettings.Converters.Add(JsonSubtypesConverterBuilder.Of<CreativeFieldSettings>("Type")
            .RegisterSubtype<DefaultCreativeFieldSettings>("Default")
            .RegisterSubtype<SelectCreativeFieldSettings>("Select")
            .Build());
        _jsonSerializer = JsonSerializer.Create(serializerSettings);
    }

    public override T FromStream<T> (Stream stream)
    {
        using (stream)
        {
            if (typeof(Stream).IsAssignableFrom(typeof(T)))
            {
                return (T)(object)stream;
            }

            using (var sr = new StreamReader(stream))
            {
                using (var jsonTextReader = new JsonTextReader(sr))
                {
                    return _jsonSerializer.Deserialize<T>(jsonTextReader)!;
                }
            }
        }
    }

    public override Stream ToStream<T> (T input)
    {
        var streamPayload = new MemoryStream();
        using (var streamWriter = new StreamWriter(streamPayload, _defaultEncoding, 1024, true))
        {
            using (JsonWriter writer = new JsonTextWriter(streamWriter))
            {
                writer.Formatting = Formatting.None;
                _jsonSerializer.Serialize(writer, input);
                writer.Flush();
                streamWriter.Flush();
            }
        }

        streamPayload.Position = 0;
        return streamPayload;
    }
}