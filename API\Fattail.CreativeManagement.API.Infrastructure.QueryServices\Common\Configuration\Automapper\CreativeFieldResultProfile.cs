﻿using AutoMapper;
using CreativeFieldEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeField;
using SelectOptionEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.SelectOption;
using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common.Configuration.AutoMapper;

internal sealed class CreativeFieldResultProfile : Profile
{
    public CreativeFieldResultProfile ()
    {
        CreateMap<CreativeFieldEntity, CreativeFieldQueryResult>()
            .ConstructUsing((src, context) =>
            {
                var settings = context.Mapper.Map<CreativeFieldSettingsResult>(src.Settings);
                var options = src.Options?.Select(o => context.Mapper.Map<SelectOptionQueryResult>(o)).ToList().AsReadOnly();

                return new CreativeFieldQueryResult(long.Parse(src.Id), src.Name, src.Type, src.Predefined, src.OmsExternalIdentifier, settings, options);
            });

        CreateMap<SelectOptionEntity, SelectOptionQueryResult>()
            .ConstructUsing(src => new SelectOptionQueryResult(long.Parse(src.Id), src.Description));
    }
}