﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common.OpenApiModel;

public class CreativeFieldRequestOpenApiModel : OpenApiSchema
{
    public CreativeFieldRequestOpenApiModel ()
    {
        Properties.Add("id", new OpenApiSchema { Type = "integer", Format = "int64" });
        Properties.Add("type", new OpenApiSchema { Type = "string" });
    }
}

public class MultiFileUploadFieldValueOpenApiModel : CreativeFieldRequestOpenApiModel
{
    public MultiFileUploadFieldValueOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(nameof(CreativeFieldTypeEnum.MultiFileUpload)) };
        Properties["type"].Default = new OpenApiString(nameof(CreativeFieldTypeEnum.MultiFileUpload));
        Properties.Add("value", new OpenApiSchema { Type = "array", Items = new OpenApiSchema { Type = "integer", Format = "int64" } });
    }
}

public class FileUploadFieldValueOpenApiModel : CreativeFieldRequestOpenApiModel
{
    public FileUploadFieldValueOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(nameof(CreativeFieldTypeEnum.FileUpload)) };
        Properties["type"].Default = new OpenApiString(nameof(CreativeFieldTypeEnum.FileUpload));
        Properties.Add("value", new OpenApiSchema { Type = "integer", Format = "int64" });
    }
}

public class SingleLineTextFieldValueOpenApiModel : CreativeFieldRequestOpenApiModel
{
    public SingleLineTextFieldValueOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(nameof(CreativeFieldTypeEnum.SingleLineText)) };
        Properties["type"].Default = new OpenApiString(nameof(CreativeFieldTypeEnum.SingleLineText));
        Properties.Add("value", new OpenApiSchema { Type = "string" });
    }
}

public class MultiLineTextFieldValueOpenApiModel : CreativeFieldRequestOpenApiModel
{
    public MultiLineTextFieldValueOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(nameof(CreativeFieldTypeEnum.MultiLineText)) };
        Properties["type"].Default = new OpenApiString(nameof(CreativeFieldTypeEnum.MultiLineText));
        Properties.Add("value", new OpenApiSchema { Type = "string" });
    }
}

public class MultiSelectOptionFieldValueOpenApiModel : CreativeFieldRequestOpenApiModel
{
    public MultiSelectOptionFieldValueOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(nameof(CreativeFieldTypeEnum.MultiSelectOption)) };
        Properties["type"].Default = new OpenApiString(nameof(CreativeFieldTypeEnum.MultiSelectOption));
        Properties.Add("value", new OpenApiSchema { Type = "array", Items = new OpenApiSchema { Type = "integer", Format = "int64" } });
    }
}

public class SingleSelectOptionFieldValueOpenApiModel : CreativeFieldRequestOpenApiModel
{
    public SingleSelectOptionFieldValueOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(nameof(CreativeFieldTypeEnum.SingleSelectOption)) };
        Properties["type"].Default = new OpenApiString(nameof(CreativeFieldTypeEnum.SingleSelectOption));
        Properties.Add("value", new OpenApiSchema { Type = "integer", Format = "int64" });
    }
}