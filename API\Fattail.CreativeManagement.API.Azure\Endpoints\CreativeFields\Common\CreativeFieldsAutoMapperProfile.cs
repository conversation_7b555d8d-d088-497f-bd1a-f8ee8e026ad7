using AutoMapper;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;

internal sealed class CreativeFieldsAutoMapperProfile : Profile
{
    public CreativeFieldsAutoMapperProfile ()
    {
        CreateMap<CreativeFieldQueryResult, CreativeFieldResponse>()
            .ConstructUsing((src, context) => src.Type switch
            {
                CreativeFieldTypeEnum.MultiSelectOption => new MultiSelectCreativeFieldResponse(
                    src.Id, src.Name, src.Type, src.Predefined, src.OmsExternalIdentifier,
                    context.Mapper.Map<SelectCreativeFieldSettingsResponse>(src.Settings),
                    src.Options?.Select(o => context.Mapper.Map<SelectOptionResponse>(o)).ToList().AsReadOnly()),
                CreativeFieldTypeEnum.SingleSelectOption => new SingleSelectCreativeFieldResponse(
                    src.Id, src.Name, src.Type, src.Predefined, src.OmsExternalIdentifier,
                    context.Mapper.Map<SelectCreativeFieldSettingsResponse>(src.Settings),
                    src.Options?.Select(o => context.Mapper.Map<SelectOptionResponse>(o)).ToList().AsReadOnly()),
                _ => new CreativeFieldResponse(
                    src.Id, src.Name, src.Type, src.Predefined, src.OmsExternalIdentifier,
                    context.Mapper.Map<CreativeFieldSettingsResponse>(src.Settings),
                    src.Options?.Select(o => context.Mapper.Map<SelectOptionResponse>(o)).ToList().AsReadOnly())
            });

        CreateMap<SelectOptionQueryResult, SelectOptionResponse>();

        CreateMap<CreativeFieldSettingsQueryResult, CreativeFieldSettingsResponse>()
            .IncludeAllDerived();
        CreateMap<DefaultCreativeFieldSettingsQueryResult, DefaultCreativeFieldSettingsResponse>();
        CreateMap<SelectCreativeFieldSettingsQueryResult, SelectCreativeFieldSettingsResponse>();
    }
}