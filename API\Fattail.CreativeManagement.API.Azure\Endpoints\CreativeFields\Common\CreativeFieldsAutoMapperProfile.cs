﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;

internal sealed class CreativeFieldsAutoMapperProfile : Profile
{
    public CreativeFieldsAutoMapperProfile ()
    {
        CreateMap<CreativeFieldQueryResult, CreativeFieldResponse>()
            .Include(typeof(MultiSelectCreativeFieldQueryResult), typeof(MultiSelectCreativeFieldResponse))
            .Include(typeof(SingleSelectCreativeFieldQueryResult), typeof(SingleSelectCreativeFieldResponse));

        CreateMap<SelectOptionQueryResult, SelectOptionResponse>();
        CreateMap<MultiSelectCreativeFieldQueryResult, MultiSelectCreativeFieldResponse>();
        CreateMap<SingleSelectCreativeFieldQueryResult, SingleSelectCreativeFieldResponse>();

        CreateMap<SelectOptionResult, SelectOptionResponse>();

        CreateMap<CreativeFieldResult, CreativeFieldResponse>();

        CreateMap<CreativeFieldSettingsResult, CreativeFieldSettingsResponse>()
            .Include(typeof(DefaultCreativeFieldSettingsResult), typeof(DefaultCreativeFieldSettingsResponse))
            .Include(typeof(SelectCreativeFieldSettingsResult), typeof(SelectCreativeFieldSettingsResponse));
        CreateMap<DefaultCreativeFieldSettingsResult, DefaultCreativeFieldSettingsResponse>();
        CreateMap<SelectCreativeFieldSettingsResult, SelectCreativeFieldSettingsResponse>();
    }
}