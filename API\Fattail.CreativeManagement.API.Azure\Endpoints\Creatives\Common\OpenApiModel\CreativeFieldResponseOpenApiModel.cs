﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common.OpenApiModel;

public class CreativeFieldResponseOpenApiModel : OpenApiSchema
{
    public CreativeFieldResponseOpenApiModel ()
    {
        Properties.Add("id", new OpenApiSchema { Type = "integer", Format = "int64" });
        Properties.Add("type", new OpenApiSchema { Type = "string" });
    }
}

public class MultiFileUploadFieldValueResponseOpenApiModel : CreativeFieldResponseOpenApiModel
{
    public MultiFileUploadFieldValueResponseOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(CreativeFieldTypeEnum.MultiFileUpload.ToString()) };
        Properties["type"].Default = new OpenApiString(CreativeFieldTypeEnum.MultiFileUpload.ToString());
        Properties.Add("value", new OpenApiSchema { Type = "array", Items = new CreativeFileResponseOpenApiModel() });
    }
}

public class FileUploadFieldValueResponseOpenApiModel : CreativeFieldResponseOpenApiModel
{
    public FileUploadFieldValueResponseOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(CreativeFieldTypeEnum.FileUpload.ToString()) };
        Properties["type"].Default = new OpenApiString(CreativeFieldTypeEnum.FileUpload.ToString());
        Properties.Add("value", new CreativeFileResponseOpenApiModel());
    }
}

public class CreativeFileResponseOpenApiModel : OpenApiSchema
{
    public CreativeFileResponseOpenApiModel ()
    {
        Properties.Add("id", new OpenApiSchema { Type = "integer", Format = "int64" });
        Properties.Add("name", new OpenApiSchema { Type = "string" });
        Properties.Add("extension", new OpenApiSchema { Type = "string" });
        Properties.Add("location", new OpenApiSchema { Type = "string" });
        Properties.Add("size", new OpenApiSchema { Type = "integer", Format = "int64" });
        Properties.Add("metadata", new OpenApiSchema { Type = "object" });
    }
}

public class SingleLineTextFieldValueResponseOpenApiModel : CreativeFieldResponseOpenApiModel
{
    public SingleLineTextFieldValueResponseOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(CreativeFieldTypeEnum.SingleLineText.ToString()) };
        Properties["type"].Default = new OpenApiString(CreativeFieldTypeEnum.SingleLineText.ToString());
        Properties.Add("value", new OpenApiSchema { Type = "string" });
    }
}

public class MultiLineTextFieldValueResponseOpenApiModel : CreativeFieldResponseOpenApiModel
{
    public MultiLineTextFieldValueResponseOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(CreativeFieldTypeEnum.MultiLineText.ToString()) };
        Properties["type"].Default = new OpenApiString(CreativeFieldTypeEnum.MultiLineText.ToString());
        Properties.Add("value", new OpenApiSchema { Type = "string" });
    }
}

public class MultiSelectOptionFieldValueResponseOpenApiModel : CreativeFieldResponseOpenApiModel
{
    public MultiSelectOptionFieldValueResponseOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(CreativeFieldTypeEnum.MultiSelectOption.ToString()) };
        Properties["type"].Default = new OpenApiString(CreativeFieldTypeEnum.MultiSelectOption.ToString());
        Properties.Add("value", new OpenApiSchema { Type = "array", Items = new OpenApiSchema { Type = "integer", Format = "int64" } });
    }
}

public class SingleSelectOptionFieldValueResponseOpenApiModel : CreativeFieldResponseOpenApiModel
{
    public SingleSelectOptionFieldValueResponseOpenApiModel ()
    {
        Properties["type"].Enum = new List<IOpenApiAny> { new OpenApiString(CreativeFieldTypeEnum.SingleSelectOption.ToString()) };
        Properties["type"].Default = new OpenApiString(CreativeFieldTypeEnum.SingleSelectOption.ToString());
        Properties.Add("value", new OpenApiSchema { Type = "integer", Format = "int64" });
    }
}
