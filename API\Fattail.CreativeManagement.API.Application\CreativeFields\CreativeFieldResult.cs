using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Application.CreativeFields
{
    public record CreativeFieldResult
    (
        long Id,
        string Name,
        CreativeFieldTypeEnum Type,
        bool Predefined,
        string? OmsExternalIdentifier,
        CreativeFieldSettingsResult Settings,
        IReadOnlyList<SelectOptionResult>? Options
    );

    public record SelectOptionResult
    (
        string Id,
        string Description
    );

    public abstract record CreativeFieldSettingsResult
    (
        string Type
    );

    public record DefaultCreativeFieldSettingsResult () : CreativeFieldSettingsResult("Default");

    public record SelectCreativeFieldSettingsResult
    (
        IReadOnlyList<SelectOptionResult> Options
    ) : CreativeFieldSettingsResult("Select");

    public record MultiSelectCreativeFieldResult
    (
        long Id,
        string Name,
        CreativeFieldTypeEnum Type,
        bool Predefined,
        string? OmsExternalIdentifier,
        CreativeFieldSettingsResult Settings,
        IReadOnlyList<SelectOptionResult>? Options
    ) : CreativeFieldResult(Id, Name, Type, Predefined, OmsExternalIdentifier, Settings, Options);

    public record SingleSelectCreativeFieldResult (
        long Id,
        string Name,
        CreativeFieldTypeEnum Type,
        bool Predefined,
        string? OmsExternalIdentifier,
        CreativeFieldSettingsResult Settings,
        IReadOnlyList<SelectOptionResult>? Options
    ) : CreativeFieldResult(Id, Name, Type, Predefined, OmsExternalIdentifier, Settings, Options);
}
