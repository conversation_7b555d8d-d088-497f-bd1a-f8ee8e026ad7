﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Application.CreativeFields
{
    public record CreativeFieldResult
    (
        long Id,
        string Name,
        CreativeFieldTypeEnum Type,
        bool Predefined,
        string? OmsExternalIdentifier,
        CreativeFieldSettingsResult Settings,
        IReadOnlyList<SelectOptionResult>? Options
    );

    public record SelectOptionResult
    (
        long Id,
        string Description
    );

    public abstract record CreativeFieldSettingsResult
    (
        string Type
    );

    public record DefaultCreativeFieldSettingsResult () : CreativeFieldSettingsResult("Default");

    public record SelectCreativeFieldSettingsResult
    (
        IReadOnlyList<SelectOptionResult> Options
    ) : CreativeFieldSettingsResult("Select");
}
