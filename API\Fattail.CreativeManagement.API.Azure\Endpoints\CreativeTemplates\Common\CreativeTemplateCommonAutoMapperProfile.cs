﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.Common;

internal sealed class CreativeTemplateCommonAutoMapperProfile : Profile
{
    public CreativeTemplateCommonAutoMapperProfile ()
    {
        CreateMap<CreativeTemplateQueryResult, CreativeTemplateResponse>();
        CreateMap<CreativeTemplateResult, CreativeTemplateResponse>();
        CreateMap<CreativeTemplateCreativeFieldResult, CreativeTemplateCreativeFieldResponse>()
            .Include(typeof(CreativeTemplateMultiSelectCreativeFieldResult), typeof(CreativeTemplateMultiSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSingleSelectCreativeFieldResult), typeof(CreativeTemplateSingleSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSectionDividerCreativeFieldResult), typeof(CreativeTemplateSectionDividerCreativeFieldResponse));

        var baseMapping = CreateMap<CreativeTemplateCreativeFieldResult, CreativeFieldResponse>()
            .ConstructUsing((src, context) => new CreativeFieldResponse(
                src.Id, src.Name, src.Type, false, null, new DefaultCreativeFieldSettingsResponse(), null))
            .Include(typeof(CreativeTemplateMultiSelectCreativeFieldResult), typeof(MultiSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSingleSelectCreativeFieldResult), typeof(SingleSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSectionDividerCreativeFieldResult), typeof(SectionDividerCreativeFieldResponse));
        baseMapping.ForAllMembers(opt => opt.Ignore());

        var multiSelectMapping = CreateMap<CreativeTemplateMultiSelectCreativeFieldResult, MultiSelectCreativeFieldResponse>()
            .ConstructUsing((src, context) => new MultiSelectCreativeFieldResponse(
                src.Id, src.Name, src.Type, false, null,
                new SelectCreativeFieldSettingsResponse(src.Options.Select(o => context.Mapper.Map<SelectOptionResponse>(o)).ToList().AsReadOnly()),
                src.Options.Select(o => context.Mapper.Map<SelectOptionResponse>(o)).ToList().AsReadOnly()));
        multiSelectMapping.ForAllMembers(opt => opt.Ignore());

        var singleSelectMapping = CreateMap<CreativeTemplateSingleSelectCreativeFieldResult, SingleSelectCreativeFieldResponse>()
            .ConstructUsing((src, context) => new SingleSelectCreativeFieldResponse(
                src.Id, src.Name, src.Type, false, null,
                new SelectCreativeFieldSettingsResponse(src.Options.Select(o => context.Mapper.Map<SelectOptionResponse>(o)).ToList().AsReadOnly()),
                src.Options.Select(o => context.Mapper.Map<SelectOptionResponse>(o)).ToList().AsReadOnly()));
        singleSelectMapping.ForAllMembers(opt => opt.Ignore());

        var sectionDividerMapping = CreateMap<CreativeTemplateSectionDividerCreativeFieldResult, SectionDividerCreativeFieldResponse>()
            .ConstructUsing((src, context) => new SectionDividerCreativeFieldResponse(
                src.Id, src.Name, src.Type, false, null, new DefaultCreativeFieldSettingsResponse(), null, src.Content));
        sectionDividerMapping.ForAllMembers(opt => opt.Ignore());

        CreateMap<ValidationRuleResult, ValidationRuleResponse>();
        CreateMap<CreativeTemplateMultiSelectCreativeFieldResult, CreativeTemplateMultiSelectCreativeFieldResponse>();
        CreateMap<CreativeTemplateSingleSelectCreativeFieldResult, CreativeTemplateSingleSelectCreativeFieldResponse>();
        CreateMap<CreativeTemplateSectionDividerCreativeFieldResult, CreativeTemplateSectionDividerCreativeFieldResponse>();
        CreateMap<CreativeTemplateSelectOptionResult, SelectOptionResponse>();

        var baseQueryMapping = CreateMap<CreativeTemplateCreativeFieldQueryResult, CreativeFieldResponse>()
            .ConstructUsing((src, context) => new CreativeFieldResponse(
                src.Id, src.Name, src.Type, false, null, new DefaultCreativeFieldSettingsResponse(), null))
            .Include(typeof(CreativeTemplateMultiSelectCreativeFieldQueryResult), typeof(MultiSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSingleSelectCreativeFieldQueryResult), typeof(SingleSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSectionDividerCreativeFieldQueryResult), typeof(SectionDividerCreativeFieldResponse));
        baseQueryMapping.ForAllMembers(opt => opt.Ignore());

        var multiSelectQueryMapping = CreateMap<CreativeTemplateMultiSelectCreativeFieldQueryResult, MultiSelectCreativeFieldResponse>()
            .ConstructUsing((src, context) => new MultiSelectCreativeFieldResponse(
                src.Id, src.Name, src.Type, false, null,
                new SelectCreativeFieldSettingsResponse(src.Options.Select(o => context.Mapper.Map<SelectOptionResponse>(o)).ToList().AsReadOnly()),
                src.Options.Select(o => context.Mapper.Map<SelectOptionResponse>(o)).ToList().AsReadOnly()));
        multiSelectQueryMapping.ForAllMembers(opt => opt.Ignore());

        var singleSelectQueryMapping = CreateMap<CreativeTemplateSingleSelectCreativeFieldQueryResult, SingleSelectCreativeFieldResponse>()
            .ConstructUsing((src, context) => new SingleSelectCreativeFieldResponse(
                src.Id, src.Name, src.Type, false, null,
                new SelectCreativeFieldSettingsResponse(src.Options.Select(o => context.Mapper.Map<SelectOptionResponse>(o)).ToList().AsReadOnly()),
                src.Options.Select(o => context.Mapper.Map<SelectOptionResponse>(o)).ToList().AsReadOnly()));
        singleSelectQueryMapping.ForAllMembers(opt => opt.Ignore());

        var sectionDividerQueryMapping = CreateMap<CreativeTemplateSectionDividerCreativeFieldQueryResult, SectionDividerCreativeFieldResponse>()
            .ConstructUsing((src, context) => new SectionDividerCreativeFieldResponse(
                src.Id, src.Name, src.Type, false, null, new DefaultCreativeFieldSettingsResponse(), null, src.Content));
        sectionDividerQueryMapping.ForAllMembers(opt => opt.Ignore());
    }
}