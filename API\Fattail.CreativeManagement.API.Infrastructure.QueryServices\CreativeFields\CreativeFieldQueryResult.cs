using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;

public record CreativeFieldQueryResult
(
    long Id,
    string Name,
    CreativeFieldTypeEnum Type,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsQueryResult Settings,
    IReadOnlyList<SelectOptionQueryResult>? Options
);

public sealed record SelectOptionQueryResult
(
    long Id,
    string Description
);

public abstract record CreativeFieldSettingsQueryResult;

public record DefaultCreativeFieldSettingsQueryResult () : CreativeFieldSettingsQueryResult;

public record SelectCreativeFieldSettingsQueryResult
(
    IReadOnlyList<SelectOptionQueryResult> Options
) : CreativeFieldSettingsQueryResult;