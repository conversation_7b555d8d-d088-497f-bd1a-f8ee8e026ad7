using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;

public record CreativeFieldQueryResult
(
    long Id,
    string Name,
    CreativeFieldTypeEnum Type,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResult Settings,
    IReadOnlyList<SelectOptionQueryResult>? Options
);

public sealed record SelectOptionQueryResult
(
    long Id,
    string Description
);