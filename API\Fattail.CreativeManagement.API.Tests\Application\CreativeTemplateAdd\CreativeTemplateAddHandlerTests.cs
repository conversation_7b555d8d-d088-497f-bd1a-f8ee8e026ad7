﻿using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateAdd;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;
using CreativeField = Fattail.CreativeManagement.API.Domain.CreativeFields.CreativeField;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativesTemplateAdd;

[TestFixture]
public class CreativeTemplateAddHandlerTests
{
    [SetUp]
    public void SetUp ()
    {
        _idManagerMock = new Mock<IIdManager>();

        _creativeTemplateRepository = new Mock<ICreativeTemplateRepository>();
        _creativeFieldRepositoryMock = new Mock<ICreativeFieldRepository>();

        _creativeTemplateAddHandler =
            new CreativeTemplateAddHandler(_idManagerMock.Object, _creativeTemplateRepository.Object,
                _creativeFieldRepositoryMock.Object);
    }

    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<ICreativeTemplateRepository> _creativeTemplateRepository = null!;
    private Mock<ICreativeFieldRepository> _creativeFieldRepositoryMock = null!;
    private CreativeTemplateAddHandler _creativeTemplateAddHandler = null!;
    private readonly string _fieldName = "fieldName";

    [Test]
    public async Task Creative_template_cant_be_created_without_valid_creative_fields ()
    {
        string templateName = "Template Name";
        var creativeFields = new List<CreativeTemplateCreativeFieldDto>
        {
            new (1, 1,  new List<ValidationRuleDto>(), null, new Dictionary<string, object?>()),
            new (2, 2, new List<ValidationRuleDto>(), null, new Dictionary<string, object?>()),
            new (3, 3, new List<ValidationRuleDto>(), null, new Dictionary<string, object?>())
        };

        var creativeTemplateAddCommand = new CreativeTemplateAddCommand(templateName, creativeFields);

        _idManagerMock.SetupSequence(idManager => idManager.GetId()).Returns(1234);
        _creativeFieldRepositoryMock.Setup(x => x.FindManyByIdAsync<CreativeField>(It.IsAny<IEnumerable<CreativeFieldId>>()))
            .ReturnsAsync([]);

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.CreateAsync<CreativeTemplateResult>(It.IsAny<CreativeTemplate>()))
            .ReturnsAsync((CreativeTemplate creativeTemplate) =>
            {
                var creativeFieldResult = creativeTemplate.CreativeFields
                    .Select(f => new CreativeTemplateCreativeFieldResult(f.Id, f.Name, f.DisplayOrder, f.Type.EnumType, new List<ValidationRuleResult>(), null)).ToList();

                return new CreativeTemplateResult
                {
                    Id = creativeTemplate.Id,
                    Name = creativeTemplate.Name,
                    CreativeType = creativeTemplate.CreativeType,
                    CreativeFields = creativeFieldResult
                };
            });

        Result<CreativeTemplateResult> result =
            await _creativeTemplateAddHandler.Handle(creativeTemplateAddCommand, CancellationToken.None);

        result.Should().BeFailure().And
            .HaveReason(new InvalidValueError("creative field id: 1", nameof(CreativeTemplateCreativeField))).And
            .HaveReason(new InvalidValueError("creative field id: 2", nameof(CreativeTemplateCreativeField))).And
            .HaveReason(new InvalidValueError("creative field id: 3", nameof(CreativeTemplateCreativeField)));
    }

    [Test]
    public async Task Creative_template_can_be_created ()
    {
        string templateName = "Template Name";
        var creativeFields = new List<CreativeTemplateCreativeFieldDto>
        {
            new (1, 1, new List<ValidationRuleDto>(), null, new Dictionary<string, object?>()),
            new (2, 2, new List<ValidationRuleDto>(), null, new Dictionary<string, object?>()),
            new (3, 3, new List<ValidationRuleDto>(), null, new Dictionary<string, object?>())
        };

        var creativeTemplateAddCommand = new CreativeTemplateAddCommand(templateName, creativeFields);

        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(1234);

        var uniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
        _creativeFieldRepositoryMock.Setup(x => x.FindManyByIdAsync<CreativeField>(It.IsAny<IEnumerable<CreativeFieldId>>()))
            .ReturnsAsync([
                CreativeField.Create(new CreativeFieldId(1), _fieldName, CreativeFieldType.SingleLineText, uniqueNameRequirement, false, null, null).Value,
                CreativeField.Create(new CreativeFieldId(2), _fieldName, CreativeFieldType.MultiFileUpload, uniqueNameRequirement, false, null, null).Value,
                CreativeField.Create(new CreativeFieldId(3), _fieldName, CreativeFieldType.MultiLineText, uniqueNameRequirement, false, null, null).Value
            ]);

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.CreateAsync<CreativeTemplateResult>(It.IsAny<CreativeTemplate>()))
            .ReturnsAsync((CreativeTemplate creativeTemplate) =>
            {
                var creativeFieldResult = creativeTemplate.CreativeFields
                    .Select(f => new CreativeTemplateCreativeFieldResult(f.Id, f.Name, f.DisplayOrder, f.Type.EnumType, new List<ValidationRuleResult>(), null)).ToList();

                return new CreativeTemplateResult
                {
                    Id = creativeTemplate.Id,
                    Name = creativeTemplate.Name,
                    CreativeType = creativeTemplate.CreativeType,
                    CreativeFields = creativeFieldResult
                };
            });

        Result<CreativeTemplateResult> result =
            await _creativeTemplateAddHandler.Handle(creativeTemplateAddCommand, CancellationToken.None);

        result.Should().BeSuccess();
        result.Value.Should().BeEquivalentTo(creativeTemplateAddCommand, opt => opt.Excluding(c => c.CreativeFields));
    }
}
