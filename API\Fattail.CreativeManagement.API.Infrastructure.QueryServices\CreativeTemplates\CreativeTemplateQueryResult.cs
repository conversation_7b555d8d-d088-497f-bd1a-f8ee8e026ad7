using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates;

public sealed record ValidationRuleQueryResult
{
    public CreativeFieldValidationRuleType Type { get; set; }
    public IReadOnlyList<string> Options { get; init; }
};

public sealed record CreativeTemplateQueryResult
{
    public long Id { get; init; }
    public string Name { get; init; }
    public CreativeFieldTypeEnum CreativeType { get; init; }
    public bool Archive { get; init; }
    public bool Predefined { get; set; }
    public long? ClonedFrom { get; init; }
    public IReadOnlyList<CreativeTemplateCreativeFieldQueryResult> CreativeFields { get; init; }
}

public record CreativeTemplateCreativeFieldQueryResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleQueryResult> ValidationRules,
    string? Tooltip,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResult Settings,
    IReadOnlyList<SelectOptionQueryResult>? Options
);


public sealed record CreativeTemplateMultiSelectCreativeFieldQueryResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleQueryResult> ValidationRules,
    string? Tooltip,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResult Settings,
    IReadOnlyList<SelectOptionQueryResult>? Options
) : CreativeTemplateCreativeFieldQueryResult(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip, Predefined, OmsExternalIdentifier, Settings, Options);

public sealed record CreativeTemplateSingleSelectCreativeFieldQueryResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleQueryResult> ValidationRules,
    string? Tooltip,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResult Settings,
    IReadOnlyList<SelectOptionQueryResult>? Options
) : CreativeTemplateCreativeFieldQueryResult(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip, Predefined, OmsExternalIdentifier, Settings, Options);

public sealed record CreativeTemplateSectionDividerCreativeFieldQueryResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleQueryResult> ValidationRules,
    string? Tooltip,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResult Settings,
    IReadOnlyList<SelectOptionQueryResult>? Options,
    string? Content
) : CreativeTemplateCreativeFieldQueryResult(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip, Predefined, OmsExternalIdentifier, Settings, Options);