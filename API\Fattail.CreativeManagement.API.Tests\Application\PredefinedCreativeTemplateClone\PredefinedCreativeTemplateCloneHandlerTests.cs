﻿using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.PredefinedCreativeTemplateClone;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Specifications;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Application.PredefinedCreativeTemplateClone;

[TestFixture]
public class PredefinedCreativeTemplateCloneHandlerTests
{
    [SetUp]
    public void SetUp ()
    {
        _idManagerMock = new Mock<IIdManager>();
        _predefinedCreativeTemplateRepositoryMock = new Mock<IPredefinedCreativeTemplateRepository>();
        _creativeTemplateRepositoryMock = new Mock<ICreativeTemplateRepository>();
        _predefinedCreativeFieldRepositoryMock = new Mock<IPredefinedCreativeFieldRepository>();
        _creativeFieldRepositoryMock = new Mock<ICreativeFieldRepository>();

        _creativeTemplateCloneHandler = new PredefinedCreativeTemplateCloneHandler(
            _idManagerMock.Object,
            _predefinedCreativeTemplateRepositoryMock.Object,
            _creativeTemplateRepositoryMock.Object,
            _predefinedCreativeFieldRepositoryMock.Object,
            _creativeFieldRepositoryMock.Object);

        _creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
    }

    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<IPredefinedCreativeTemplateRepository> _predefinedCreativeTemplateRepositoryMock = null!;
    private Mock<ICreativeTemplateRepository> _creativeTemplateRepositoryMock = null!;
    private Mock<IPredefinedCreativeFieldRepository> _predefinedCreativeFieldRepositoryMock = null!;
    private Mock<ICreativeFieldRepository> _creativeFieldRepositoryMock = null!;
    private PredefinedCreativeTemplateCloneHandler _creativeTemplateCloneHandler = null!;
    private CreativeFieldUniqueNameRequirement _creativeFieldUniqueNameRequirement = null!;

    [Test]
    public async Task Clone_fails_when_predefined_template_not_found ()
    {
        var command = new PredefinedCreativeTemplateCloneCommand(123, "Cloned Template");

        _predefinedCreativeTemplateRepositoryMock
            .Setup(x => x.FindByIdAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync((CreativeTemplate?)null);

        Result<CreativeTemplateResult> result = await _creativeTemplateCloneHandler.Handle(command, CancellationToken.None);

        result.Should().BeFailure();
        result.Errors.First().Should().BeOfType<EntityNotFoundError>();
    }


    [Test]
    public async Task Clone_succeeds_with_valid_predefined_template ()
    {
        var command = new PredefinedCreativeTemplateCloneCommand(123, "Cloned Template");
        CreativeTemplate predefinedTemplate = CreateTestTemplateWithFields(123, "Predefined Template", CreativeType.Image, true);

        _idManagerMock.SetupSequence(x => x.GetId())
            .Returns(999)
            .Returns(1001)
            .Returns(1002);

        _predefinedCreativeTemplateRepositoryMock
            .Setup(x => x.FindByIdAsync(new CreativeTemplateId(123)))
            .ReturnsAsync(predefinedTemplate);

        _creativeTemplateRepositoryMock
            .Setup(x => x.FindAsync(It.IsAny<PredefinedTemplateAlreadyClonedSpecification>()))
            .ReturnsAsync((CreativeTemplate?)null);

        SetupFieldMocks();

        _creativeTemplateRepositoryMock
            .Setup(x => x.CreateAsync<CreativeTemplateResult>(It.IsAny<CreativeTemplate>()))
            .ReturnsAsync((CreativeTemplate template) => CreateMockTemplateResult(template));

        Result<CreativeTemplateResult> result = await _creativeTemplateCloneHandler.Handle(command, CancellationToken.None);

        result.Should().BeSuccess();
        result.Value.Name.Should().Be("Cloned Template");
        result.Value.CreativeType.Should().Be(CreativeType.Image);
    }

    private CreativeTemplate CreateTestTemplateWithFields (long id, string name, CreativeType type, bool predefined)
    {
        CreativeTemplateName templateName = CreativeTemplateName.Create(name, new CreativeTemplateUniqueNameRequirement(true)).Value;

        CreativeField field1 = CreativeField.Create(new CreativeFieldId(1), "Field 1", CreativeFieldType.SingleLineText, _creativeFieldUniqueNameRequirement, predefined, null, null).Value;
        CreativeField field2 = CreativeField.Create(new CreativeFieldId(2), "Field 2", CreativeFieldType.FileUpload, _creativeFieldUniqueNameRequirement, predefined, null, null).Value;

        var createRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(id),
            templateName,
            type,
            new List<DisplayOrderCreativeField>
            {
                new(new CreativeFieldId(1), 1),
                new(new CreativeFieldId(2), 2)
            },
            new HashSet<CreativeField> { field1, field2 },
            predefined);

        return CreativeTemplateFactory.Create(createRequest).Value;
    }

    private void SetupFieldMocks ()
    {
        CreativeField predefinedField1 = CreativeField.Create(new CreativeFieldId(1), "Field 1", CreativeFieldType.SingleLineText, _creativeFieldUniqueNameRequirement, true, null, null).Value;
        CreativeField predefinedField2 = CreativeField.Create(new CreativeFieldId(2), "Field 2", CreativeFieldType.FileUpload, _creativeFieldUniqueNameRequirement, true, null, null).Value;

        _predefinedCreativeFieldRepositoryMock
            .Setup(x => x.FindByIdAsync(new CreativeFieldId(1)))
            .ReturnsAsync(predefinedField1);

        _predefinedCreativeFieldRepositoryMock
            .Setup(x => x.FindByIdAsync(new CreativeFieldId(2)))
            .ReturnsAsync(predefinedField2);

        _creativeFieldRepositoryMock
            .Setup(x => x.FindByIdAsync(It.IsAny<CreativeFieldId>()))
            .ReturnsAsync((CreativeField?)null);

        _creativeFieldRepositoryMock
            .Setup(x => x.CreateAsync<CreativeFieldResult>(It.IsAny<CreativeField>()))
            .ReturnsAsync((CreativeField field) => new CreativeFieldResult(
                field.Id,
                field.Name,
                field.Type.EnumType,
                field.Predefined,
                field.OmsExternalIdentifier,
                new DefaultCreativeFieldSettingsResult(),
                null
            ));
    }

    private CreativeTemplateResult CreateMockTemplateResult (CreativeTemplate template)
    {
        return new CreativeTemplateResult
        {
            Id = template.Id,
            Name = template.Name,
            CreativeType = template.CreativeType,
            CreativeFields = template.CreativeFields.Select(f => new CreativeTemplateCreativeFieldResult(
                f.Id, f.Name, f.DisplayOrder, f.Type.EnumType, new List<ValidationRuleResult>(), f.Tooltip)).ToList()
        };
    }
}

