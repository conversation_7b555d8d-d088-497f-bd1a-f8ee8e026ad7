using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;

namespace Fattail.CreativeManagement.API.Application.CreativeTemplates;

public sealed record CreativeTemplateResult
{
    public long Id { get; init; }

    public string Name { get; init; }

    public CreativeType CreativeType { get; init; }

    public bool Archive { get; init; }

    public bool Predefined { get; init; }

    public long? ClonedFrom { get; init; }

    public IReadOnlyList<CreativeTemplateCreativeFieldResult> CreativeFields { get; init; }
}

public sealed record ValidationRuleResult
{
    public CreativeFieldValidationRuleType Type { get; init; }

    public IReadOnlyList<string> Options { get; init; }
};

public record CreativeTemplateCreativeFieldResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleResult> ValidationRules,
    string? Tooltip,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResult Settings,
    IReadOnlyList<CreativeTemplateSelectOptionResult>? Options
);

public record CreativeTemplateSelectOptionResult
(
    long Id,
    string Description
);

public record CreativeTemplateMultiSelectCreativeFieldResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleResult> ValidationRules,
    string? Tooltip,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResult Settings,
    IReadOnlyList<CreativeTemplateSelectOptionResult>? Options
) : CreativeTemplateCreativeFieldResult(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip, Predefined, OmsExternalIdentifier, Settings, Options);

public record CreativeTemplateSingleSelectCreativeFieldResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleResult> ValidationRules,
    string? Tooltip,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResult Settings,
    IReadOnlyList<CreativeTemplateSelectOptionResult>? Options
) : CreativeTemplateCreativeFieldResult(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip, Predefined, OmsExternalIdentifier, Settings, Options);

public record CreativeTemplateSectionDividerCreativeFieldResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleResult> ValidationRules,
    string? Tooltip,
    bool Predefined,
    string? OmsExternalIdentifier,
    CreativeFieldSettingsResult Settings,
    IReadOnlyList<CreativeTemplateSelectOptionResult>? Options,
    string? Content
) : CreativeTemplateCreativeFieldResult(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip, Predefined, OmsExternalIdentifier, Settings, Options);